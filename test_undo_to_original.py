#!/usr/bin/env python3
"""
测试脚本：验证撤销功能只能撤销到原始数据
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QComboBox, QWidget, QVBoxLayout, QPushButton, QLabel, QTextEdit
from PySide6.QtCore import Qt

class TestUndoToOriginal(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("测试撤销到原始数据功能")
        self.setGeometry(100, 100, 500, 400)
        
        # 创建布局
        layout = QVBoxLayout(self)
        
        # 标题
        title = QLabel("测试撤销功能：最多撤销到原始识别结果")
        title.setStyleSheet("font-size: 16pt; font-weight: bold;")
        layout.addWidget(title)
        
        # 模拟原始识别结果
        self.emotion_labels = ['(无)', '<|HAPPY|>', '<|SAD|>', '<|ANGRY|>', '<|NEUTRAL|>']
        self.emotion_combo = QComboBox()
        self.emotion_combo.addItems(self.emotion_labels)
        
        # 设置原始值为 '<|HAPPY|>' (模拟识别结果)
        self.original_emotion = '<|HAPPY|>'
        self.emotion_combo.setCurrentText(self.original_emotion)
        self.emotion_combo.currentTextChanged.connect(self.on_emotion_changed)
        
        layout.addWidget(QLabel(f"情感标签 (原始值: {self.original_emotion}):"))
        layout.addWidget(self.emotion_combo)
        
        # 撤销/恢复按钮
        self.undo_button = QPushButton("撤销")
        self.undo_button.clicked.connect(self.undo_action)
        self.undo_button.setEnabled(False)
        
        self.redo_button = QPushButton("恢复")
        self.redo_button.clicked.connect(self.redo_action)
        self.redo_button.setEnabled(False)
        
        layout.addWidget(self.undo_button)
        layout.addWidget(self.redo_button)
        
        # 状态显示
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(200)
        self.status_text.setReadOnly(True)
        layout.addWidget(QLabel("操作日志:"))
        layout.addWidget(self.status_text)
        
        # 历史记录
        self.edit_history = []
        self.redo_history = []
        
        # 当前值记录
        self.emotion_current = self.original_emotion
        
        self.status_text.append(f"初始化完成，原始值: {self.original_emotion}")
        self.status_text.append("请尝试：")
        self.status_text.append("1. 修改情感标签到其他值")
        self.status_text.append("2. 再次修改到另一个值")
        self.status_text.append("3. 点击撤销，观察是否只能撤销到原始值")
    
    def on_emotion_changed(self, new_text):
        old_text = self.emotion_current
        if old_text != new_text:
            self.record_edit(old_text, new_text)
            self.emotion_current = new_text
            self.status_text.append(f"修改: '{old_text}' -> '{new_text}'")
    
    def record_edit(self, old_text, new_text):
        edit_record = {
            'old_text': old_text,
            'new_text': new_text
        }
        self.edit_history.append(edit_record)
        self.redo_history = []  # 清空重做历史
        self.update_button_states()
    
    def undo_action(self):
        if not self.edit_history:
            return
        
        # 获取最后一次编辑操作
        edit_record = self.edit_history[-1]
        
        # 检查是否撤销到原始数据
        if edit_record['old_text'] == self.original_emotion:
            self.status_text.append(f"⚠️ 已经是原始识别结果 '{self.original_emotion}'，无法继续撤销")
            return
        
        # 执行撤销
        edit_record = self.edit_history.pop()
        self.redo_history.append(edit_record)
        
        # 恢复旧值
        self.emotion_combo.currentTextChanged.disconnect()
        self.emotion_combo.setCurrentText(edit_record['old_text'])
        self.emotion_current = edit_record['old_text']
        self.emotion_combo.currentTextChanged.connect(self.on_emotion_changed)
        
        self.status_text.append(f"✅ 撤销: 恢复到 '{edit_record['old_text']}'")
        self.update_button_states()
    
    def redo_action(self):
        if not self.redo_history:
            return
        
        redo_record = self.redo_history.pop()
        self.edit_history.append(redo_record)
        
        # 恢复新值
        self.emotion_combo.currentTextChanged.disconnect()
        self.emotion_combo.setCurrentText(redo_record['new_text'])
        self.emotion_current = redo_record['new_text']
        self.emotion_combo.currentTextChanged.connect(self.on_emotion_changed)
        
        self.status_text.append(f"↩️ 恢复: 设置为 '{redo_record['new_text']}'")
        self.update_button_states()
    
    def update_button_states(self):
        # 检查是否可以撤销（不能撤销到原始值之前）
        can_undo = False
        if self.edit_history:
            last_edit = self.edit_history[-1]
            can_undo = last_edit['old_text'] != self.original_emotion
        
        self.undo_button.setEnabled(can_undo)
        self.redo_button.setEnabled(len(self.redo_history) > 0)
        
        # 更新按钮文本显示状态
        undo_text = "撤销"
        if self.edit_history:
            last_edit = self.edit_history[-1]
            if last_edit['old_text'] == self.original_emotion:
                undo_text = "撤销 (已到原始值)"
        self.undo_button.setText(undo_text)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = TestUndoToOriginal()
    window.show()
    
    print("测试说明:")
    print("1. 原始值设置为 '<|HAPPY|>'")
    print("2. 修改情感标签到其他值（如 '<|SAD|>'）")
    print("3. 再次修改到另一个值（如 '<|ANGRY|>'）")
    print("4. 点击撤销，应该能撤销到 '<|SAD|>'")
    print("5. 再次点击撤销，应该能撤销到原始值 '<|HAPPY|>'")
    print("6. 再次点击撤销，应该提示无法继续撤销")
    
    sys.exit(app.exec())
