#!/usr/bin/env python3
"""
测试脚本：验证撤销/恢复功能的修复
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PySide6.QtWidgets import QApplication, QComboBox, QWidget, QVBoxLayout, QPushButton, QLabel
from PySide6.QtCore import Qt

class TestComboUndoRedo(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("测试下拉框撤销/恢复功能")
        self.setGeometry(100, 100, 400, 300)
        
        # 创建布局
        layout = QVBoxLayout(self)
        
        # 标题
        title = QLabel("测试下拉框撤销/恢复功能")
        title.setStyleSheet("font-size: 16pt; font-weight: bold;")
        layout.addWidget(title)
        
        # 情感标签下拉框
        self.emotion_labels = ['(无)', '<|HAPPY|>', '<|SAD|>', '<|ANGRY|>', '<|NEUTRAL|>', '<|FEARFUL|>', '<|DISGUSTED|>', '<|SURPRISED|>']
        self.emotion_combo = QComboBox()
        self.emotion_combo.addItems(self.emotion_labels)
        self.emotion_combo.currentTextChanged.connect(self.on_emotion_changed)
        
        layout.addWidget(QLabel("情感标签:"))
        layout.addWidget(self.emotion_combo)
        
        # 事件标签下拉框
        self.event_labels = ['(无)', '<|BGM|>', '<|Speech|>', '<|Applause|>', '<|Laughter|>', '<|Cry|>', '<|Sneeze|>', '<|Breath|>', '<|Cough|>']
        self.event_combo = QComboBox()
        self.event_combo.addItems(self.event_labels)
        self.event_combo.currentTextChanged.connect(self.on_event_changed)
        
        layout.addWidget(QLabel("事件标签:"))
        layout.addWidget(self.event_combo)
        
        # 撤销/恢复按钮
        self.undo_button = QPushButton("撤销")
        self.undo_button.clicked.connect(self.undo_action)
        self.undo_button.setEnabled(False)
        
        self.redo_button = QPushButton("恢复")
        self.redo_button.clicked.connect(self.redo_action)
        self.redo_button.setEnabled(False)
        
        layout.addWidget(self.undo_button)
        layout.addWidget(self.redo_button)
        
        # 状态显示
        self.status_label = QLabel("状态: 初始化完成")
        layout.addWidget(self.status_label)
        
        # 历史记录
        self.edit_history = []
        self.redo_history = []
        
        # 当前值记录
        self.emotion_current = "(无)"
        self.event_current = "(无)"
        
    def on_emotion_changed(self, new_text):
        old_text = self.emotion_current
        if old_text != new_text:
            self.record_edit('emotion', old_text, new_text)
            self.emotion_current = new_text
            self.status_label.setText(f"情感标签: '{old_text}' -> '{new_text}'")
    
    def on_event_changed(self, new_text):
        old_text = self.event_current
        if old_text != new_text:
            self.record_edit('event', old_text, new_text)
            self.event_current = new_text
            self.status_label.setText(f"事件标签: '{old_text}' -> '{new_text}'")
    
    def record_edit(self, combo_type, old_text, new_text):
        edit_record = {
            'type': combo_type,
            'old_text': old_text,
            'new_text': new_text
        }
        self.edit_history.append(edit_record)
        self.redo_history = []  # 清空重做历史
        self.update_button_states()
    
    def undo_action(self):
        if not self.edit_history:
            return
        
        edit_record = self.edit_history.pop()
        self.redo_history.append(edit_record)
        
        # 恢复旧值
        if edit_record['type'] == 'emotion':
            self.emotion_combo.currentTextChanged.disconnect()
            self.emotion_combo.setCurrentText(edit_record['old_text'])
            self.emotion_current = edit_record['old_text']
            self.emotion_combo.currentTextChanged.connect(self.on_emotion_changed)
        elif edit_record['type'] == 'event':
            self.event_combo.currentTextChanged.disconnect()
            self.event_combo.setCurrentText(edit_record['old_text'])
            self.event_current = edit_record['old_text']
            self.event_combo.currentTextChanged.connect(self.on_event_changed)
        
        self.status_label.setText(f"撤销: 恢复到 '{edit_record['old_text']}'")
        self.update_button_states()
    
    def redo_action(self):
        if not self.redo_history:
            return
        
        redo_record = self.redo_history.pop()
        self.edit_history.append(redo_record)
        
        # 恢复新值
        if redo_record['type'] == 'emotion':
            self.emotion_combo.currentTextChanged.disconnect()
            self.emotion_combo.setCurrentText(redo_record['new_text'])
            self.emotion_current = redo_record['new_text']
            self.emotion_combo.currentTextChanged.connect(self.on_emotion_changed)
        elif redo_record['type'] == 'event':
            self.event_combo.currentTextChanged.disconnect()
            self.event_combo.setCurrentText(redo_record['new_text'])
            self.event_current = redo_record['new_text']
            self.event_combo.currentTextChanged.connect(self.on_event_changed)
        
        self.status_label.setText(f"恢复: 设置为 '{redo_record['new_text']}'")
        self.update_button_states()
    
    def update_button_states(self):
        self.undo_button.setEnabled(len(self.edit_history) > 0)
        self.redo_button.setEnabled(len(self.redo_history) > 0)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = TestComboUndoRedo()
    window.show()
    
    print("测试说明:")
    print("1. 选择不同的情感标签和事件标签")
    print("2. 点击撤销按钮，观察是否正确恢复到之前的值")
    print("3. 点击恢复按钮，观察是否正确恢复到撤销前的值")
    print("4. 验证'(无)'选项是否正确显示，而不是空白")
    
    sys.exit(app.exec())
