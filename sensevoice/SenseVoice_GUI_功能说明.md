# SenseVoice GUI 可编辑表格版本

## 🌟 新功能特性

### 📊 智能表格显示
- **6列表格结构**：
  - 编号
  - 开始时间
  - 结束时间  
  - **情感标签** (可编辑)
  - **事件标签** (可编辑)
  - **识别文本** (可编辑)

### 🎭 情感标签支持
SenseVoice能够识别以下情感：
- `<|HAPPY|>` - 快乐
- `<|SAD|>` - 悲伤
- `<|ANGRY|>` - 愤怒
- `<|NEUTRAL|>` - 中性
- `<|FEARFUL|>` - 恐惧
- `<|DISGUSTED|>` - 厌恶
- `<|SURPRISED|>` - 惊讶

### 🎵 事件标签支持
SenseVoice能够检测以下事件：
- `<|Speech|>` - 语音
- `<|BGM|>` - 背景音乐
- `<|Applause|>` - 掌声
- `<|Laughter|>` - 笑声
- `<|Cry|>` - 哭声
- `<|Sneeze|>` - 打喷嚏
- `<|Breath|>` - 呼吸声
- `<|Cough|>` - 咳嗽
- `<|Event_UNK|>` - 未知事件

## ✏️ 编辑功能

### 🔧 实时编辑
- **双击编辑**：双击情感标签、事件标签或识别文本列进行编辑
- **自动格式化**：输入标签时自动添加 `<|` 和 `|>` 格式
- **只读保护**：编号、时间戳列不可编辑

### ⏪ 撤销/恢复系统
- **撤销按钮**：撤销上一次编辑操作
- **恢复按钮**：恢复被撤销的操作
- **多级历史**：支持最多50步操作历史
- **智能按钮**：按钮状态根据历史自动启用/禁用

### 🔄 重置功能
- **重置到原始**：一键恢复到原始识别结果
- **保留原始数据**：始终保存初始识别结果用于重置

## 📥 下载功能

### 📄 多格式导出
- **下载TXT**：纯文本格式，包含情感和事件标签
- **下载SRT**：字幕格式，适用于视频字幕

### 🔄 实时同步
- 编辑内容实时反映到下载文件中
- 支持从表格数据生成下载内容

## 🖱️ 右键菜单
- **编辑此项**：快速进入编辑模式
- **复制此行**：复制整行数据到剪贴板

## 🎯 使用场景

### 1. 情感分析
- 检查音频中的情感变化
- 手动修正情感标签识别错误
- 分析说话人的情感状态

### 2. 事件检测
- 标识音频中的特殊事件
- 区分语音和背景声音
- 过滤非语音事件

### 3. 内容编辑
- 修正识别错误的文本
- 添加或删除标点符号
- 调整文本格式

## 🚀 操作流程

1. **选择音频文件**
2. **选择设备和模型**
3. **开始识别** → 生成带标签的表格
4. **编辑内容** → 双击需要修改的单元格
5. **使用撤销/恢复** → 随时纠正编辑错误
6. **下载结果** → 选择TXT或SRT格式

## 💡 使用技巧

- **标签格式**：输入标签时可以省略 `<|` 和 `|>`，系统会自动添加
- **批量操作**：使用右键菜单快速复制行数据
- **历史管理**：善用撤销/恢复功能进行精细编辑
- **格式预览**：实时预览下载文件的格式效果 